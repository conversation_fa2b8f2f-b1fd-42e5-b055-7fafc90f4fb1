{"name": "freelancehub-client", "version": "1.0.0", "description": "Frontend client for FreelanceHub marketplace", "type": "module", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "react-hook-form": "^7.45.4", "@hookform/resolvers": "^3.3.1", "zod": "^3.22.2", "axios": "^1.5.0", "zustand": "^4.4.1", "socket.io-client": "^4.7.2", "@stripe/stripe-js": "^2.1.6", "@stripe/react-stripe-js": "^2.1.1", "react-hot-toast": "^2.4.1", "react-icons": "^4.10.1", "react-loading-skeleton": "^3.3.1", "react-intersection-observer": "^9.5.2", "date-fns": "^2.30.0", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.4", "vite": "^4.4.9", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "eslint": "^8.48.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.4"}, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\""}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}